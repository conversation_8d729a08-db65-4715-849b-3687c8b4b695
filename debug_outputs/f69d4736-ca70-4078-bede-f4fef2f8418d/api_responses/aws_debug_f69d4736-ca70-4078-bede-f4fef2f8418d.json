{"timestamp": "2025-06-22T10:02:31.467685", "engine": "aws", "request_id": "f69d4736-ca70-4078-bede-f4fef2f8418d", "data": {"blocks_count": 61, "text_blocks": ["SAMPLE INVOICE", "Invoice #: INV-2024-001", "Date: January 15, 2024", "Bill To:", "<PERSON>", "123 Main Street", "Anytown, ST 12345", "Description", "Qty", "Price", "Total", "WidgetA", "2 $10.00 $20.00", "Widget B", "1 $15.00 $15.00", "Subtotal", "$35.00", "Tax (8%):", "$2.80", "Total:", "$37.80"], "confidence_distribution": [97.87517547607422, 97.93599700927734, 98.71154022216797, 81.22200012207031, 99.88610076904297, 99.96717834472656, 87.61061096191406, 99.96014404296875, 99.83079528808594, 99.92028045654297, 99.92028045654297, 90.21783447265625, 99.7968521118164, 93.4288330078125, 99.40960693359375, 66.19160461425781, 99.79093933105469, 97.94004821777344, 99.6827163696289, 85.74079132080078, 99.*********], "result": {"engine_name": "aws", "status": "success", "text": "SAMPLE INVOICE\nInvoice #: INV-2024-001\nDate: January 15, 2024\nBill To:\n<PERSON>\n123 Main Street\nAnytown, ST 12345\nDescription\nQty\nPrice\nTotal\nWidgetA\n2 $10.00 $20.00\nWidget B\n1 $15.00 $15.00\nSubtotal\n$35.00\nTax (8%):\n$2.80\nTotal:\n$37.80", "confidence": 0.9500047774541946, "word_count": 39, "processing_time_ms": 2152.7771949768066, "cost": 0.0015, "error_message": null, "fallback_reason": null, "regions": null, "tables": null, "key_value_pairs": null}}}