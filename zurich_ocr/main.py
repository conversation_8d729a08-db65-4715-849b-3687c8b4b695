"""
Enhanced FastAPI OCR Application with comprehensive debug support
"""
import uuid
import time
import asyncio
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

from fastapi import FastAPI, File, UploadFile, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import structlog

from .core.config import settings, debug_config
from .core.vision_analyzer import VisionAnalyzer
from .core.cv_processor import CVProcessor
from .core.ocr_engines import OCREngineManager
from .models.request_models import DocumentConfig, OCRRequest, BatchOCRRequest
from .models.response_models import (
    OCRResponse, ProcessingMetadata, DebugInformation, 
    HealthCheckResponse, ConfigResponse, BatchOCRResponse,
    ProcessingStatus, VisionAnalysisResult, PreprocessingResult
)
from .utils.error_handlers import setup_error_handlers
from .utils.document_utils import DocumentProcessor
from .utils.monitoring import setup_monitoring


# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


# Create FastAPI application
app = FastAPI(
    title="Zurich Challenge - Winning OCR API",
    description="Hybrid Intelligence OCR Solution with OpenAI Vision + Multi-Engine Processing + Comprehensive Debug Mode",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG_MODE else None,
    redoc_url="/redoc" if settings.DEBUG_MODE else None
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

if not settings.DEBUG_MODE:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "*.yourdomain.com"]
    )

# Initialize components
vision_analyzer = VisionAnalyzer(settings.OPENAI_API_KEY)
cv_processor = CVProcessor()
ocr_manager = OCREngineManager()
document_processor = DocumentProcessor()

# Setup error handlers and monitoring
setup_error_handlers(app)
if settings.ENABLE_METRICS:
    setup_monitoring(app)


@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    logger.info("Starting Zurich OCR Engine", version="1.0.0", debug_mode=settings.DEBUG_MODE)
    
    # Create debug directories if needed
    if settings.DEBUG_MODE:
        debug_dir = Path(settings.DEBUG_OUTPUT_DIR)
        debug_dir.mkdir(parents=True, exist_ok=True)
        logger.info("Debug mode enabled", debug_dir=str(debug_dir))
    
    # Health check all OCR engines
    engine_health = await ocr_manager.health_check_all()
    logger.info("OCR engines health check", engines=engine_health)


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("Shutting down Zurich OCR Engine")


@app.post("/api/v1/extract-text", response_model=OCRResponse)
async def extract_text(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    config: Optional[str] = None
):
    """
    Main OCR processing endpoint with hybrid intelligence and comprehensive debug support
    """
    request_id = str(uuid.uuid4())
    start_time = time.time()
    processing_started = datetime.now()
    
    # Setup debug session
    debug_session = debug_config.setup_debug_session(request_id)
    
    logger.info(
        "OCR request started",
        request_id=request_id,
        filename=file.filename,
        file_size=file.size,
        debug_enabled=debug_session.get("debug_enabled", False)
    )
    
    try:
        # Parse configuration
        document_config = DocumentConfig()
        if config:
            import json
            config_dict = json.loads(config)
            document_config = DocumentConfig(**config_dict)
        
        # Validate file
        await document_processor.validate_file(file, settings)
        
        # Read file data
        file_data = await file.read()
        
        # Initialize debug information
        debug_info = None
        if document_config.debug.enabled or settings.DEBUG_MODE:
            debug_info = DebugInformation(
                session_id=request_id,
                debug_enabled=True,
                processing_steps=[],
                intermediate_files=[],
                api_calls=[],
                error_logs=[]
            )
        
        # Step 1: Vision Analysis (if enabled and cost-effective)
        vision_analysis = None
        if document_config.vision_analysis.enabled:
            should_use_vision = vision_analyzer.should_use_vision(
                len(file_data), 
                "medium",  # Default complexity
                document_config.vision_analysis.cost_threshold
            )
            
            if should_use_vision:
                logger.info("Starting vision analysis", request_id=request_id)
                vision_analysis = await vision_analyzer.analyze_document(
                    file_data, 
                    request_id,
                    document_config.vision_analysis.model,
                    document_config.vision_analysis.detail_level
                )
                
                # Save vision analysis for debug
                if debug_info:
                    debug_info.vision_request_data = {
                        "model": document_config.vision_analysis.model,
                        "detail_level": document_config.vision_analysis.detail_level
                    }
                    debug_info.vision_response_data = vision_analysis.dict()
        
        # Step 2: Determine processing strategy
        processing_strategy = determine_processing_strategy(document_config, vision_analysis)
        
        # Step 3: Computer Vision Preprocessing
        preprocessing_result = None
        processed_image = file_data
        
        if document_config.preprocessing.enabled:
            logger.info("Starting image preprocessing", request_id=request_id)
            processed_image, preprocessing_result = await cv_processor.process_image(
                file_data, 
                processing_strategy,
                request_id,
                save_intermediates=document_config.debug.save_intermediate_images or settings.SAVE_INTERMEDIATE_IMAGES
            )
            
            # Save preprocessing info for debug
            if debug_info:
                debug_info.preprocessing_visualizations = preprocessing_result.steps_applied
        
        # Step 4: OCR Engine Selection & Processing
        selected_engine = select_ocr_engine(document_config, processing_strategy)
        
        logger.info(
            "Starting OCR processing", 
            engine=selected_engine, 
            request_id=request_id
        )
        
        ocr_result = await ocr_manager.extract_text(
            processed_image, 
            selected_engine, 
            {
                **processing_strategy,
                "confidence_threshold": document_config.ocr_strategy.confidence_threshold,
                "include_regions": document_config.output.include_regions,
                "extract_tables": document_config.output.extract_tables,
                "fallback_enabled": document_config.ocr_strategy.fallback_enabled
            },
            request_id
        )
        
        # Step 5: Post-processing and structured extraction
        structured_data = None
        if document_config.output.structured_extraction:
            # Add structured data extraction logic here
            pass
        
        # Step 6: Build comprehensive response
        processing_time = (time.time() - start_time) * 1000
        processing_completed = datetime.now()
        
        # Create performance metrics
        performance_metrics = create_performance_metrics(
            processing_time, vision_analysis, preprocessing_result, ocr_result
        )
        
        # Create cost breakdown
        cost_breakdown = create_cost_breakdown(vision_analysis, ocr_result)
        
        # Create metadata
        metadata = ProcessingMetadata(
            request_id=request_id,
            processing_time_ms=processing_time,
            engine_used=ocr_result.engine_name,
            engines_attempted=[ocr_result.engine_name],  # Could be expanded for fallbacks
            vision_analysis_used=vision_analysis is not None,
            preprocessing_applied=preprocessing_result.steps_applied if preprocessing_result else [],
            confidence_score=ocr_result.confidence,
            document_type=vision_analysis.document_type if vision_analysis else None,
            language_detected=vision_analysis.language if vision_analysis else None,
            page_count=1,  # Could be enhanced for multi-page documents
            performance_metrics=performance_metrics,
            cost_breakdown=cost_breakdown,
            timestamp=processing_completed
        )
        
        # Finalize debug information
        if debug_info and (document_config.debug.enabled or settings.DEBUG_MODE):
            # Add final debug data
            debug_info.performance_profile = performance_metrics.dict() if performance_metrics else None
            
            # Save comprehensive debug summary
            if settings.COLLECT_ALL_OUTPUTS:
                background_tasks.add_task(
                    save_debug_summary,
                    request_id,
                    debug_info,
                    metadata,
                    document_config
                )
        
        # Create response
        response = OCRResponse(
            success=True,
            status=ProcessingStatus.SUCCESS,
            extracted_text=ocr_result.text,
            structured_data=structured_data,
            regions=ocr_result.regions,
            vision_analysis=vision_analysis,
            preprocessing_result=preprocessing_result,
            ocr_results=[ocr_result],
            metadata=metadata,
            debug_info=debug_info if document_config.debug.enabled else None,
            timestamp=processing_completed,
            processing_started=processing_started,
            processing_completed=processing_completed
        )
        
        logger.info(
            "OCR request completed successfully",
            request_id=request_id,
            processing_time_ms=processing_time,
            confidence=ocr_result.confidence,
            engine_used=ocr_result.engine_name,
            total_cost=cost_breakdown.total_cost if cost_breakdown else 0.0
        )
        
        return response
        
    except Exception as e:
        processing_time = (time.time() - start_time) * 1000
        
        logger.error(
            "OCR request failed",
            request_id=request_id,
            error=str(e),
            processing_time_ms=processing_time
        )
        
        # Create error response
        error_metadata = ProcessingMetadata(
            request_id=request_id,
            processing_time_ms=processing_time,
            engine_used="none",
            engines_attempted=[],
            vision_analysis_used=False,
            preprocessing_applied=[],
            confidence_score=0.0,
            timestamp=datetime.now()
        )
        
        return OCRResponse(
            success=False,
            status=ProcessingStatus.FAILED,
            extracted_text="",
            metadata=error_metadata,
            timestamp=datetime.now(),
            processing_started=processing_started,
            processing_completed=datetime.now()
        )


def determine_processing_strategy(config: DocumentConfig, vision_analysis: Optional[VisionAnalysisResult]) -> Dict[str, Any]:
    """Determine optimal processing strategy based on config and vision analysis"""
    strategy = {
        "preprocessing_needed": ["enhance"],
        "recommended_ocr": "tesseract",
        "complexity": "medium",
        "document_type": "unknown",
        "has_tables": False,
        "has_handwriting": False
    }

    if vision_analysis:
        strategy.update({
            "preprocessing_needed": vision_analysis.preprocessing_needed,
            "recommended_ocr": vision_analysis.recommended_ocr,
            "complexity": vision_analysis.complexity,
            "document_type": vision_analysis.document_type,
            "has_tables": vision_analysis.has_tables,
            "has_handwriting": vision_analysis.has_handwriting
        })

    # Override with user config if specified
    if config.ocr_strategy.engine.value != "auto":
        strategy["recommended_ocr"] = config.ocr_strategy.engine.value

    if config.preprocessing.profile.value != "auto":
        strategy["preprocessing_needed"] = cv_processor.preprocessing_profiles.get(
            config.preprocessing.profile.value, ["enhance"]
        )

    return strategy


def select_ocr_engine(config: DocumentConfig, strategy: Dict[str, Any]) -> str:
    """Select optimal OCR engine based on strategy"""
    if config.ocr_strategy.engine.value != "auto":
        return config.ocr_strategy.engine.value

    recommended = strategy.get("recommended_ocr", "tesseract")

    # Zurich-specific optimizations
    if strategy.get("complexity") == "complex" or strategy.get("has_handwriting"):
        return "google"  # Best for complex documents
    elif strategy.get("has_tables"):
        return "aws"     # Best for tables
    else:
        return recommended


def create_performance_metrics(
    total_time: float,
    vision_analysis: Optional[VisionAnalysisResult],
    preprocessing_result: Optional[PreprocessingResult],
    ocr_result
) -> Optional[Dict[str, Any]]:
    """Create performance metrics"""
    if not settings.DETAILED_TIMING:
        return None

    return {
        "total_processing_time_ms": total_time,
        "vision_analysis_time_ms": vision_analysis.processing_time_ms if vision_analysis else 0.0,
        "preprocessing_time_ms": preprocessing_result.processing_time_ms if preprocessing_result else 0.0,
        "ocr_processing_time_ms": ocr_result.processing_time_ms,
        "post_processing_time_ms": 0.0,  # Could be enhanced
        "overall_confidence": ocr_result.confidence,
        "text_quality_score": 0.85  # Could be calculated
    }


def create_cost_breakdown(
    vision_analysis: Optional[VisionAnalysisResult],
    ocr_result
) -> Optional[Dict[str, Any]]:
    """Create cost breakdown"""
    if not settings.TRACK_COSTS:
        return None

    vision_cost = vision_analysis.cost_estimate if vision_analysis else 0.0
    ocr_cost = ocr_result.cost

    return {
        "vision_api_cost": vision_cost,
        "ocr_engine_cost": ocr_cost,
        "preprocessing_cost": 0.0,  # Preprocessing is free
        "total_cost": vision_cost + ocr_cost,
        "currency": "USD",
        "cost_per_page": vision_cost + ocr_cost
    }


async def save_debug_summary(
    request_id: str,
    debug_info: DebugInformation,
    metadata: ProcessingMetadata,
    config: DocumentConfig
):
    """Save comprehensive debug summary"""
    try:
        if debug_config.session_id:
            summary = {
                "request_id": request_id,
                "timestamp": datetime.now().isoformat(),
                "configuration": config.dict(),
                "metadata": metadata.dict(),
                "debug_info": debug_info.dict(),
                "session_summary": {
                    "total_processing_time_ms": metadata.processing_time_ms,
                    "engines_used": metadata.engines_attempted,
                    "vision_analysis_used": metadata.vision_analysis_used,
                    "preprocessing_applied": metadata.preprocessing_applied,
                    "final_confidence": metadata.confidence_score,
                    "total_cost": metadata.cost_breakdown.total_cost if metadata.cost_breakdown else 0.0
                }
            }

            debug_path = debug_config.get_debug_path(
                f"session_summary_{request_id}.json",
                "logs"
            )

            import json
            with open(debug_path, 'w') as f:
                json.dump(summary, f, indent=2)

    except Exception as e:
        logger.error("Failed to save debug summary", error=str(e), request_id=request_id)


@app.post("/api/v1/batch-extract", response_model=BatchOCRResponse)
async def batch_extract_text(
    request: BatchOCRRequest,
    background_tasks: BackgroundTasks
):
    """Batch processing endpoint for multiple documents"""
    batch_id = str(uuid.uuid4())
    started_at = datetime.now()

    logger.info(
        "Batch OCR request started",
        batch_id=batch_id,
        document_count=len(request.documents)
    )

    # Process documents in parallel (limited concurrency)
    semaphore = asyncio.Semaphore(settings.MAX_WORKERS)

    async def process_single_document(doc_path: str):
        async with semaphore:
            # Implementation would process individual document
            # This is a placeholder
            pass

    # Start batch processing in background
    background_tasks.add_task(process_batch_documents, batch_id, request)

    return BatchOCRResponse(
        batch_id=batch_id,
        total_documents=len(request.documents),
        processed_documents=0,
        failed_documents=0,
        results=[],
        batch_metadata={"status": "processing"},
        started_at=started_at
    )


async def process_batch_documents(batch_id: str, request: BatchOCRRequest):
    """Background task for batch processing"""
    # Implementation for batch processing
    pass


@app.get("/api/v1/health", response_model=HealthCheckResponse)
async def health_check():
    """Comprehensive health check endpoint"""
    start_time = time.time()

    # Check OCR engines
    engine_health = await ocr_manager.health_check_all()

    # Get system metrics
    system_metrics = {
        "memory_usage_mb": 0,  # Could be implemented
        "cpu_usage_percent": 0,  # Could be implemented
        "disk_usage_percent": 0,  # Could be implemented
        "active_requests": 0  # Could be implemented
    }

    # Calculate uptime (simplified)
    uptime_seconds = time.time() - start_time

    return HealthCheckResponse(
        status="healthy" if all(engine_health.values()) else "degraded",
        timestamp=datetime.now(),
        version="1.0.0",
        engines_available=list(ocr_manager.engines.keys()),
        engines_healthy=engine_health,
        system_metrics=system_metrics,
        uptime_seconds=uptime_seconds
    )


@app.get("/api/v1/config", response_model=ConfigResponse)
async def get_config():
    """Get current configuration and capabilities"""
    return ConfigResponse(
        max_file_size=settings.MAX_FILE_SIZE,
        supported_formats=settings.SUPPORTED_FORMATS,
        available_engines=list(ocr_manager.engines.keys()),
        default_engine=settings.DEFAULT_OCR_ENGINE,
        vision_enabled=bool(settings.OPENAI_API_KEY),
        debug_mode_available=True,
        cost_tracking_enabled=settings.TRACK_COSTS,
        features={
            "vision_analysis": bool(settings.OPENAI_API_KEY),
            "preprocessing": True,
            "multi_engine_fallback": True,
            "batch_processing": True,
            "debug_mode": settings.DEBUG_MODE,
            "cost_tracking": settings.TRACK_COSTS,
            "performance_monitoring": settings.ENABLE_METRICS
        }
    )


@app.get("/api/v1/stats")
async def get_statistics():
    """Get engine and processing statistics"""
    engine_stats = ocr_manager.get_engine_statistics()
    vision_stats = vision_analyzer.get_cost_summary()

    return {
        "engine_statistics": engine_stats,
        "vision_api_statistics": vision_stats,
        "system_statistics": {
            "total_requests": engine_stats["total_requests"],
            "debug_sessions": len(list(Path(settings.DEBUG_OUTPUT_DIR).glob("*"))) if Path(settings.DEBUG_OUTPUT_DIR).exists() else 0
        }
    }


@app.get("/api/v1/debug/{session_id}")
async def get_debug_session(session_id: str):
    """Get debug information for a specific session"""
    if not settings.DEBUG_MODE:
        raise HTTPException(status_code=403, detail="Debug mode not enabled")

    debug_dir = Path(settings.DEBUG_OUTPUT_DIR) / session_id
    if not debug_dir.exists():
        raise HTTPException(status_code=404, detail="Debug session not found")

    # Collect debug files
    debug_files = {
        "images": list((debug_dir / "images").glob("*")) if (debug_dir / "images").exists() else [],
        "logs": list((debug_dir / "logs").glob("*")) if (debug_dir / "logs").exists() else [],
        "api_responses": list((debug_dir / "api_responses").glob("*")) if (debug_dir / "api_responses").exists() else []
    }

    return {
        "session_id": session_id,
        "debug_files": {k: [str(f.name) for f in v] for k, v in debug_files.items()},
        "session_path": str(debug_dir)
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="debug" if settings.DEBUG_MODE else "info"
    )
