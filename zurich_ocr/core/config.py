"""
Enhanced configuration management with debug mode support
"""
from pydantic_settings import BaseSettings
from typing import Optional, List, Dict, Any
from pathlib import Path
import os
from enum import Enum


class LogLevel(str, Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"


class ProcessingMode(str, Enum):
    AUTO = "auto"
    FAST = "fast"
    ACCURATE = "accurate"
    COST_OPTIMIZED = "cost_optimized"
    DEBUG = "debug"


class Settings(BaseSettings):
    # API Keys
    OPENAI_API_KEY: str
    GOOGLE_APPLICATION_CREDENTIALS: Optional[str] = None
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_REGION: str = "us-east-1"
    AZURE_FORM_RECOGNIZER_ENDPOINT: Optional[str] = None
    AZURE_FORM_RECOGNIZER_KEY: Optional[str] = None
    
    # Processing Settings
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    SUPPORTED_FORMATS: List[str] = ["pdf", "png", "jpg", "jpeg", "tiff", "bmp", "webp"]
    
    # Performance Settings
    VISION_COST_THRESHOLD: float = 0.005  # Use vision if cost < $0.005
    DEFAULT_OCR_ENGINE: str = "tesseract"
    ENABLE_FALLBACKS: bool = True
    PARALLEL_PROCESSING: bool = True
    MAX_WORKERS: int = 4
    
    # Debug Mode Settings
    DEBUG_MODE: bool = False
    DEBUG_OUTPUT_DIR: str = "./debug_outputs"
    SAVE_INTERMEDIATE_IMAGES: bool = False
    SAVE_API_RESPONSES: bool = False
    DETAILED_TIMING: bool = False
    COLLECT_ALL_OUTPUTS: bool = False
    
    # Logging Configuration
    LOG_LEVEL: LogLevel = LogLevel.INFO
    LOG_FORMAT: str = "json"
    LOG_FILE: Optional[str] = None
    
    # Security Settings
    API_KEY_HEADER: str = "X-API-Key"
    RATE_LIMIT_PER_MINUTE: int = 60
    ENABLE_CORS: bool = True
    ALLOWED_ORIGINS: List[str] = ["*"]
    
    # Cache Settings
    ENABLE_CACHE: bool = True
    CACHE_TTL: int = 3600  # 1 hour
    REDIS_URL: Optional[str] = None
    
    # Monitoring Settings
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    
    # Cost Tracking
    TRACK_COSTS: bool = True
    COST_ALERT_THRESHOLD: float = 10.0  # Alert if daily cost exceeds $10
    
    class Config:
        env_file = ".env"
        case_sensitive = True


class DebugConfig:
    """Enhanced debug configuration for comprehensive output collection"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.debug_dir = Path(settings.DEBUG_OUTPUT_DIR)
        self.session_id = None
        
    def setup_debug_session(self, request_id: str) -> Dict[str, Any]:
        """Setup debug session with unique identifier"""
        self.session_id = request_id
        
        if self.settings.DEBUG_MODE:
            # Create debug directories
            session_dir = self.debug_dir / request_id
            session_dir.mkdir(parents=True, exist_ok=True)
            
            # Create subdirectories for different types of outputs
            (session_dir / "images").mkdir(exist_ok=True)
            (session_dir / "api_responses").mkdir(exist_ok=True)
            (session_dir / "logs").mkdir(exist_ok=True)
            (session_dir / "metrics").mkdir(exist_ok=True)
            
            return {
                "debug_enabled": True,
                "session_dir": str(session_dir),
                "collect_images": self.settings.SAVE_INTERMEDIATE_IMAGES,
                "collect_api_responses": self.settings.SAVE_API_RESPONSES,
                "detailed_timing": self.settings.DETAILED_TIMING,
                "collect_all_outputs": self.settings.COLLECT_ALL_OUTPUTS
            }
        
        return {"debug_enabled": False}
    
    def get_debug_path(self, filename: str, subdir: str = "") -> Path:
        """Get path for debug output file"""
        if not self.session_id:
            raise ValueError("Debug session not initialized")
            
        base_path = self.debug_dir / self.session_id
        if subdir:
            base_path = base_path / subdir
            
        return base_path / filename


# Global settings instance
settings = Settings()
debug_config = DebugConfig(settings)


# Cost tracking for different services
COST_PER_TOKEN = {
    "gpt-4-vision-preview": 0.01 / 1000,  # $0.01 per 1K tokens
    "gpt-4o": 0.005 / 1000,  # $0.005 per 1K tokens
    "gpt-4o-mini": 0.00015 / 1000,  # $0.00015 per 1K tokens
}

COST_PER_IMAGE = {
    "gpt-4-vision-preview": 0.00765,  # Base cost per image
    "gpt-4o": 0.00765,
    "gpt-4o-mini": 0.00255,
}

OCR_ENGINE_COSTS = {
    "tesseract": 0.0,  # Free
    "google": 0.0015,  # $1.50 per 1000 pages
    "aws": 0.0015,     # $1.50 per 1000 pages
    "azure": 0.001,    # $1.00 per 1000 pages
}


def get_estimated_cost(engine: str, page_count: int = 1, use_vision: bool = False) -> float:
    """Calculate estimated processing cost"""
    cost = 0.0
    
    # OCR engine cost
    if engine in OCR_ENGINE_COSTS:
        cost += OCR_ENGINE_COSTS[engine] * page_count
    
    # Vision API cost
    if use_vision:
        cost += COST_PER_IMAGE.get("gpt-4o-mini", 0.00255)  # Use most cost-effective vision model
    
    return cost
